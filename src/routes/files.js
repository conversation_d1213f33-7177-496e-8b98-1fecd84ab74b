import { Router } from 'express';
import <PERSON><PERSON> from 'joi';
import { FileController } from '../controllers/FileController.js';
import { validateParams, validateQuery } from '../middleware/validation.js';
import { rateLimitPerUser } from '../middleware/auth.js';

const router = Router();

// Validation schemas
const fileIdSchema = Joi.object({
  fileId: Joi.string().required().min(1).max(255)
});

const downloadQuerySchema = Joi.object({
  download: Joi.string().valid('true', 'false').optional()
});

/**
 * Serve file directly through our secure API
 * GET /api/files/:fileId
 */
router.get('/:fileId',
  validateParams(fileIdSchema),
  rateLimitPerUser(100, 60 * 1000), // 100 file access requests per minute
  FileController.getFileAccess
);

export default router;
